using SkiaSharp;

namespace SkiaSample
{
    public partial class MainForm : Form
    {
        private SKBitmap? originalBitmap;
        private SKBitmap? processedBitmap;
        private GpuImageProcessor? imageProcessor;
        private ImageStitcher? imageStitcher;
        private List<string> selectedImagePaths = new List<string>();

        public MainForm()
        {
            InitializeComponent();
            InitializeImageProcessor();
            InitializeFormSettings();
        }

        private void InitializeFormSettings()
        {
            // 设置默认选中项
            cmbEffects.SelectedIndex = 0;
            cmbAspectRatio.SelectedIndex = 4; // 默认选择"16:9 (宽屏)"

            // 默认启用GPU加速
            chkGpuAcceleration.Checked = true;
        }

        private void InitializeImageProcessor()
        {
            try
            {
                imageProcessor = new GpuImageProcessor();
                imageStitcher = new ImageStitcher();

                // 初始化GPU上下文
                imageStitcher.InitializeGpuContext();

                // 检查GPU是否真正可用
                bool gpuAvailable = imageStitcher.IsGpuAvailable();

                if (gpuAvailable)
                {
                    lblStatus.Text = "图像处理器初始化成功 (GPU加速已启用)";
                }
                else
                {
                    lblStatus.Text = "图像处理器初始化成功 (GPU不可用，将使用CPU渲染)";

                    // 显示GPU不可用的详细信息
                    var message = "GPU加速不可用，可能的原因：\n\n" +
                                "1. 显卡驱动程序过旧或未正确安装\n" +
                                "2. OpenGL版本不支持（需要OpenGL 3.3+）\n" +
                                "3. 应用程序运行在集成显卡上而非独立显卡\n" +
                                "4. Windows图形设置限制了GPU访问\n" +
                                "5. 防病毒软件阻止了OpenGL上下文创建\n\n" +
                                "建议解决方案：\n" +
                                "• 更新显卡驱动程序到最新版本\n" +
                                "• 在NVIDIA控制面板/AMD设置中强制应用使用独立显卡\n" +
                                "• 检查Windows图形设置，确保应用可以使用GPU\n\n" +
                                "应用程序将继续使用CPU渲染模式。";

                    MessageBox.Show(message, "GPU加速不可用", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // 默认禁用GPU加速选项
                    chkGpuAcceleration.Checked = false;
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"初始化失败: {ex.Message}";
                MessageBox.Show($"图像处理器初始化失败: {ex.Message}", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void TrackBarIntensity_ValueChanged(object? sender, EventArgs e)
        {
            lblIntensity.Text = $"强度: {trackBarIntensity.Value}%";
        }

        private void ChkGpuAcceleration_CheckedChanged(object? sender, EventArgs e)
        {
            if (chkGpuAcceleration.Checked)
            {
                lblStatus.Text = "GPU加速已启用";
            }
            else
            {
                lblStatus.Text = "GPU加速已禁用，使用CPU渲染";
            }
        }

        private void UpdatePictureBox()
        {
            var bitmap = processedBitmap ?? originalBitmap;
            if (bitmap != null)
            {
                // 将 SKBitmap 转换为 System.Drawing.Bitmap
                using var image = SKImage.FromBitmap(bitmap);
                using var data = image.Encode(SKEncodedImageFormat.Jpeg, 100);
                using var stream = new MemoryStream(data.ToArray());
                pictureBox.Image?.Dispose(); // 释放之前的图像
                pictureBox.Image = new Bitmap(stream);
            }
            else
            {
                pictureBox.Image?.Dispose();
                pictureBox.Image = null;
            }
        }

        private void BtnLoadImage_Click(object? sender, EventArgs e)
        {
            using var openFileDialog = new OpenFileDialog()
            {
                Filter = "图片文件|*.jpg;*.jpeg;*.png;*.bmp;*.gif|所有文件|*.*",
                Title = "选择图片文件"
            };

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    lblStatus.Text = "正在加载图片...";
                    Application.DoEvents();

                    // 开始计时
                    var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                    // 使用 SkiaSharp 加载图片
                    using var stream = File.OpenRead(openFileDialog.FileName);
                    originalBitmap?.Dispose();
                    processedBitmap?.Dispose();

                    originalBitmap = SKBitmap.Decode(stream);
                    processedBitmap = null;

                    // 停止计时
                    stopwatch.Stop();

                    if (originalBitmap != null)
                    {
                        var elapsedTime = stopwatch.ElapsedMilliseconds;
                        lblStatus.Text = $"图片加载成功: {originalBitmap.Width}x{originalBitmap.Height} - 耗时: {elapsedTime}ms";
                        btnSaveImage.Enabled = true; // 加载图片后即可保存
                        UpdatePictureBox();
                    }
                    else
                    {
                        var elapsedTime = stopwatch.ElapsedMilliseconds;
                        lblStatus.Text = $"图片加载失败 - 耗时: {elapsedTime}ms";
                    }
                }
                catch (Exception ex)
                {
                    lblStatus.Text = $"加载图片时出错: {ex.Message}";
                    MessageBox.Show($"加载图片时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnApplyEffect_Click(object? sender, EventArgs e)
        {
            if (originalBitmap == null)
            {
                MessageBox.Show("请先加载图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (imageProcessor == null)
            {
                MessageBox.Show("GPU 处理器未初始化", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            try
            {
                lblStatus.Text = "正在处理图片...";
                Application.DoEvents();

                // 开始计时
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                var effectType = cmbEffects.SelectedItem?.ToString() ?? "模糊";
                var intensity = trackBarIntensity.Value / 100.0f;

                processedBitmap?.Dispose();
                // 根据用户选择使用GPU加速或CPU渲染
                processedBitmap = imageProcessor.ApplyEffect(originalBitmap, effectType, intensity, chkGpuAcceleration.Checked);

                // 停止计时
                stopwatch.Stop();

                if (processedBitmap != null)
                {
                    var elapsedTime = stopwatch.ElapsedMilliseconds;
                    var renderMode = chkGpuAcceleration.Checked ? "GPU加速" : "CPU渲染";
                    lblStatus.Text = $"效果应用成功: {effectType} ({renderMode}) - 耗时: {elapsedTime}ms";
                    btnSaveImage.Enabled = true;
                    UpdatePictureBox();
                }
                else
                {
                    var elapsedTime = stopwatch.ElapsedMilliseconds;
                    var renderMode = chkGpuAcceleration.Checked ? "GPU加速" : "CPU渲染";
                    lblStatus.Text = $"效果应用失败 ({renderMode}) - 耗时: {elapsedTime}ms";
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"处理图片时出错: {ex.Message}";
                MessageBox.Show($"处理图片时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSaveImage_Click(object? sender, EventArgs e)
        {
            // 优先保存处理后的图片，如果没有则保存原图
            var bitmapToSave = processedBitmap ?? originalBitmap;

            if (bitmapToSave == null)
            {
                MessageBox.Show("没有图片可保存", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            using var saveFileDialog = new SaveFileDialog()
            {
                Filter = "PNG 图片|*.png|JPEG 图片|*.jpg|BMP 图片|*.bmp",
                Title = "保存处理后的图片",
                DefaultExt = "png"
            };

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    lblStatus.Text = "正在保存图片...";
                    Application.DoEvents();

                    var format = Path.GetExtension(saveFileDialog.FileName).ToLower() switch
                    {
                        ".jpg" or ".jpeg" => SKEncodedImageFormat.Jpeg,
                        ".bmp" => SKEncodedImageFormat.Bmp,
                        _ => SKEncodedImageFormat.Png
                    };

                    using var image = SKImage.FromBitmap(bitmapToSave);
                    using var data = image.Encode(format, 90);
                    using var stream = File.OpenWrite(saveFileDialog.FileName);
                    data.SaveTo(stream);

                    var imageType = processedBitmap != null ? "处理后的图片" : "拼接图片";
                    lblStatus.Text = $"{imageType}保存成功";
                    MessageBox.Show($"{imageType}保存成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    lblStatus.Text = $"保存图片时出错: {ex.Message}";
                    MessageBox.Show($"保存图片时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnLoadMultipleImages_Click(object? sender, EventArgs e)
        {
            using var openFileDialog = new OpenFileDialog()
            {
                Filter = "图片文件|*.jpg;*.jpeg;*.png;*.bmp;*.gif|所有文件|*.*",
                Title = "选择多张图片文件",
                Multiselect = true
            };

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                // 开始计时
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                selectedImagePaths.Clear();
                selectedImagePaths.AddRange(openFileDialog.FileNames);

                // 更新列表框显示
                listBoxImages.Items.Clear();
                foreach (var path in selectedImagePaths)
                {
                    listBoxImages.Items.Add(Path.GetFileName(path));
                }

                // 停止计时
                stopwatch.Stop();

                btnStitchImages.Enabled = selectedImagePaths.Count > 0;
                btnBenchmark.Enabled = selectedImagePaths.Count > 0;

                var elapsedTime = stopwatch.ElapsedMilliseconds;
                lblStatus.Text = $"已选择 {selectedImagePaths.Count} 张图片 - 耗时: {elapsedTime}ms";
            }
        }

        private void BtnStitchImages_Click(object? sender, EventArgs e)
        {
            if (selectedImagePaths.Count == 0)
            {
                MessageBox.Show("请先选择要拼接的图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (imageStitcher == null)
            {
                MessageBox.Show("图片拼接器未初始化", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            try
            {
                lblStatus.Text = "正在拼接图片...";
                Application.DoEvents();

                // 开始计时
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                int rows = (int)numericUpDownRows.Value;
                int columns = (int)numericUpDownColumns.Value;

                // 获取选择的比例
                var aspectRatio = GetSelectedAspectRatio();

                // 根据用户选择使用GPU加速或CPU渲染
                var result = chkGpuAcceleration.Checked
                    ? imageStitcher.StitchWithGpuAcceleration(selectedImagePaths, rows, columns, aspectRatio)
                    : imageStitcher.StitchWithCpuRendering(selectedImagePaths, rows, columns, aspectRatio);

                // 停止计时
                stopwatch.Stop();

                if (result.StitchedImage != null)
                {
                    // 释放之前的图片
                    originalBitmap?.Dispose();
                    processedBitmap?.Dispose();

                    originalBitmap = result.StitchedImage;
                    processedBitmap = null;

                    UpdatePictureBox();
                    btnSaveImage.Enabled = true;

                    // 显示详细信息，包含详细的性能数据
                    var aspectRatioText = aspectRatio == ImageStitcher.AspectRatio.Auto ? "自动" : $"{result.ActualAspectRatio:F2}:1";
                    var uiElapsedTime = stopwatch.ElapsedMilliseconds; // UI层总耗时（包含界面更新）

                    // 显示详细的性能信息：UI总耗时 + 内部详细耗时
                    lblStatus.Text = $"{result.Message} - 尺寸: {result.OutputWidth}x{result.OutputHeight}, 比例: {aspectRatioText} | UI总耗时: {uiElapsedTime}ms ({result.PerformanceDetails})";
                }
                else
                {
                    var elapsedTime = stopwatch.ElapsedMilliseconds;
                    lblStatus.Text = $"{result.Message} - 总耗时: {elapsedTime}ms";
                    MessageBox.Show(result.Message, "拼接失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"拼接图片时出错: {ex.Message}";
                MessageBox.Show($"拼接图片时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private ImageStitcher.AspectRatio GetSelectedAspectRatio()
        {
            return cmbAspectRatio.SelectedIndex switch
            {
                0 => ImageStitcher.AspectRatio.Auto,
                1 => ImageStitcher.AspectRatio.Square,
                2 => ImageStitcher.AspectRatio.Standard,
                3 => ImageStitcher.AspectRatio.Classic,
                4 => ImageStitcher.AspectRatio.Widescreen,
                5 => ImageStitcher.AspectRatio.Widescreen16_10,
                6 => ImageStitcher.AspectRatio.UltraWide,
                _ => ImageStitcher.AspectRatio.Auto
            };
        }

        private void BtnBenchmark_Click(object? sender, EventArgs e)
        {
            if (selectedImagePaths.Count == 0)
            {
                MessageBox.Show("请先选择要测试的图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                lblStatus.Text = "正在进行性能测试...";
                Application.DoEvents();

                int rows = (int)numericUpDownRows.Value;
                int columns = (int)numericUpDownColumns.Value;

                // 获取选择的比例
                var aspectRatio = GetSelectedAspectRatio();

                // 执行性能测试
                var (skiaGpuTime, skiaCpuTime, gdiTime, report) = ImageStitcher.BenchmarkStitching(selectedImagePaths, rows, columns, aspectRatio);

                // 显示测试结果
                var resultForm = new Form()
                {
                    Text = "性能测试结果",
                    Size = new Size(500, 400),
                    StartPosition = FormStartPosition.CenterParent,
                    FormBorderStyle = FormBorderStyle.FixedDialog,
                    MaximizeBox = false,
                    MinimizeBox = false
                };

                var textBox = new TextBox()
                {
                    Multiline = true,
                    ReadOnly = true,
                    ScrollBars = ScrollBars.Vertical,
                    Dock = DockStyle.Fill,
                    Font = new Font("Consolas", 10),
                    Text = report
                };

                resultForm.Controls.Add(textBox);
                resultForm.ShowDialog(this);

                // 更新状态
                var speedup = gdiTime.TotalMilliseconds / skiaCpuTime.TotalMilliseconds;
                lblStatus.Text = $"性能测试完成 - SkiaSharp CPU 比 GDI+ 快 {speedup:F2}x";
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"性能测试失败: {ex.Message}";
                MessageBox.Show($"性能测试时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            originalBitmap?.Dispose();
            processedBitmap?.Dispose();
            imageProcessor?.Dispose();
            imageStitcher?.Dispose();
            pictureBox.Image?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
