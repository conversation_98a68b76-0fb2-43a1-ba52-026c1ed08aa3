# Git 配置文件说明

本项目包含了完整的 Git 配置文件，用于确保代码质量和版本控制的一致性。

## 📁 配置文件概览

### `.gitignore`
**作用**：告诉 Git 哪些文件和目录应该被忽略，不纳入版本控制。

**包含的忽略规则**：
- **构建输出**：`bin/`, `obj/`, `Debug/`, `Release/` 等
- **IDE 文件**：`.vs/`, `.vscode/`, `.idea/` 等
- **用户特定文件**：`*.user`, `*.suo`, `*.userprefs` 等
- **临时文件**：`*.tmp`, `*.log`, `temp/` 等
- **包管理**：`packages/`, `node_modules/` 等
- **二进制文件**：`*.dll`, `*.exe`, `*.pdb` 等
- **项目特定**：临时测试图片、性能测试结果等

### `.gitattributes`
**作用**：定义文件的属性，控制 Git 如何处理不同类型的文件。

**主要功能**：
- **行尾处理**：自动处理不同操作系统的行尾差异
- **文件类型识别**：正确识别文本文件和二进制文件
- **合并策略**：为项目文件设置合并策略
- **语言检测**：帮助 GitHub 正确识别项目语言
- **差异显示**：为 C# 文件提供更好的差异显示

**关键设置**：
```
*.cs text diff=csharp        # C# 文件使用 C# 差异显示
*.sln text eol=crlf         # 解决方案文件使用 Windows 行尾
*.png binary                # 图片文件标记为二进制
*.Designer.cs linguist-generated=true  # 标记生成的文件
```

### `.editorconfig`
**作用**：统一不同编辑器和 IDE 的代码格式设置。

**主要配置**：
- **缩进**：C# 文件使用 4 个空格，XML 文件使用 2 个空格
- **行尾**：Windows 项目使用 CRLF
- **编码**：统一使用 UTF-8
- **C# 代码风格**：括号位置、空格使用、命名约定等
- **格式化规则**：自动格式化代码的规则

## 🚀 使用方法

### 初始化 Git 仓库
```bash
# 如果还没有初始化 Git 仓库
git init

# 添加所有文件
git add .

# 提交初始版本
git commit -m "Initial commit: .NET 9 WinForms GPU 图像处理应用"
```

### 检查忽略文件是否生效
```bash
# 查看 Git 状态，确认不需要的文件被忽略
git status

# 查看所有文件（包括被忽略的）
git status --ignored
```

### 验证文件属性
```bash
# 检查文件属性设置
git check-attr -a filename

# 查看特定属性
git check-attr text *.cs
```

## 📋 最佳实践

### 提交前检查
1. **运行构建**：确保代码能正常编译
   ```bash
   dotnet build
   ```

2. **检查格式**：确保代码格式符合 .editorconfig 规则

3. **查看更改**：检查即将提交的内容
   ```bash
   git diff --staged
   ```

### 分支管理
```bash
# 创建功能分支
git checkout -b feature/new-feature

# 切换回主分支
git checkout main

# 合并分支
git merge feature/new-feature
```

### 标签管理
```bash
# 创建版本标签
git tag -a v1.0.0 -m "Version 1.0.0: 初始发布版本"

# 推送标签
git push origin v1.0.0
```

## 🔧 自定义配置

### 添加项目特定忽略规则
在 `.gitignore` 文件末尾添加：
```
# 项目特定忽略
MyCustomFolder/
*.custom
```

### 修改代码风格
在 `.editorconfig` 中调整 C# 代码风格：
```ini
[*.cs]
csharp_new_line_before_open_brace = none  # 括号不换行
csharp_style_var_elsewhere = true:suggestion  # 更多使用 var
```

### 设置文件属性
在 `.gitattributes` 中添加：
```
# 自定义文件类型
*.myext text eol=lf
*.data binary
```

## 📊 文件统计

当前配置覆盖的文件类型：
- **代码文件**：C#, VB.NET, F#, XML, JSON
- **项目文件**：.csproj, .sln, .props, .targets
- **图像文件**：PNG, JPG, BMP, GIF, ICO
- **文档文件**：Markdown, TXT, PDF
- **配置文件**：JSON, YAML, INI, CONFIG

## 🛠️ 故障排除

### 文件仍然被跟踪
如果某些文件应该被忽略但仍在被跟踪：
```bash
# 停止跟踪文件但保留本地副本
git rm --cached filename

# 停止跟踪整个目录
git rm -r --cached directory/
```

### 行尾问题
如果遇到行尾问题：
```bash
# 重新规范化所有文件
git add --renormalize .
git commit -m "Normalize line endings"
```

### EditorConfig 不生效
1. 确保您的编辑器支持 EditorConfig
2. 重启编辑器
3. 检查 .editorconfig 文件语法

这些配置文件将帮助您维护一个干净、一致的代码库，并确保团队协作的顺畅进行。
