# Auto detect text files and perform LF normalization
* text=auto

# Explicitly declare text files you want to always be normalized and converted
# to native line endings on checkout.
*.cs text diff=csharp
*.csx text diff=csharp
*.vb text
*.vbx text
*.fs text
*.fsx text
*.fsi text
*.ml text
*.mli text

# Declare files that will always have CRLF line endings on checkout.
*.sln text eol=crlf
*.csproj text eol=crlf
*.vbproj text eol=crlf
*.vcxproj text eol=crlf
*.vcproj text eol=crlf
*.dbproj text eol=crlf
*.fsproj text eol=crlf
*.lsproj text eol=crlf
*.wixproj text eol=crlf
*.modelproj text eol=crlf
*.sqlproj text eol=crlf
*.wwaproj text eol=crlf
*.xproj text eol=crlf
*.props text eol=crlf
*.targets text eol=crlf
*.ruleset text eol=crlf
*.config text eol=crlf
*.nuspec text eol=crlf

# Set default behavior for command prompt diff.
#
# This is need for earlier builds of msysgit that does not have it on by
# default for csharp files.
# Note: This is only used by command line
*.cs diff=csharp

# Set the merge driver for project and solution files
#
# Merging from the command prompt will add diff markers to the files if there
# are conflicts (Merging from VS is not affected by the settings below, in VS
# the diff markers are never inserted). Diff markers may cause the following 
# file extensions to fail to load in VS. An alternative would be to treat
# these files as binary and thus will always conflict and require user
# intervention with every merge. To do so, just uncomment the entries below
*.sln merge=ours
*.csproj merge=ours
*.vbproj merge=ours
*.vcxproj merge=ours
*.vcproj merge=ours
*.dbproj merge=ours
*.fsproj merge=ours
*.lsproj merge=ours
*.wixproj merge=ours
*.modelproj merge=ours
*.sqlproj merge=ours
*.wwaproj merge=ours

# Alternatively, you can auto-normalize line endings for all text-based files
# *.* text=auto

# Or you can specify the line ending type you prefer for text-based files
# *.* text eol=crlf
# *.* text eol=lf

# You can also specify binary files to ensure they are not modified
*.dll binary
*.exe binary
*.pdb binary
*.lib binary
*.obj binary
*.bin binary
*.exp binary
*.pch binary
*.idb binary
*.res binary
*.suo binary
*.user binary
*.aps binary
*.pch binary
*.vspscc binary
*.vssscc binary
*.builds binary
*.pidb binary
*.svclog binary
*.scc binary
*.cache binary
*.docstates binary
*.winmd binary
*.manifest binary
*.res binary
*.pri binary
*.nupkg binary
*.appx binary
*.appxbundle binary
*.appxupload binary
*.pfx binary
*.cer binary
*.p12 binary
*.p7b binary
*.p7c binary
*.p7m binary
*.p7s binary

# Image files
*.jpg binary
*.jpeg binary
*.png binary
*.gif binary
*.bmp binary
*.ico binary
*.tiff binary
*.tif binary
*.svg text
*.webp binary

# Font files
*.ttf binary
*.otf binary
*.woff binary
*.woff2 binary
*.eot binary

# Audio files
*.mp3 binary
*.wav binary
*.ogg binary
*.flac binary

# Video files
*.mp4 binary
*.avi binary
*.mov binary
*.wmv binary
*.flv binary
*.webm binary

# Archive files
*.zip binary
*.tar binary
*.gz binary
*.rar binary
*.7z binary

# Document files
*.pdf binary
*.doc binary
*.docx binary
*.xls binary
*.xlsx binary
*.ppt binary
*.pptx binary

# Database files
*.db binary
*.sqlite binary
*.sqlite3 binary
*.mdb binary
*.accdb binary

# Specific to this project
# Sample images should be treated as binary
Images/*.png binary
Images/*.jpg binary
Images/*.jpeg binary
Images/*.bmp binary
Images/*.gif binary

# Resource files
*.resx text eol=crlf
*.Designer.cs text eol=crlf linguist-generated=true

# XML files
*.xml text eol=crlf
*.xaml text eol=crlf
*.xsd text eol=crlf
*.xslt text eol=crlf
*.wsdl text eol=crlf

# JSON files
*.json text eol=lf

# Markdown files
*.md text eol=lf
*.markdown text eol=lf

# Text files
*.txt text eol=crlf
*.rtf text eol=crlf

# Script files
*.ps1 text eol=crlf
*.cmd text eol=crlf
*.bat text eol=crlf
*.sh text eol=lf

# Web files
*.html text eol=lf
*.htm text eol=lf
*.css text eol=lf
*.js text eol=lf
*.ts text eol=lf
*.jsx text eol=lf
*.tsx text eol=lf

# Configuration files
*.ini text eol=crlf
*.cfg text eol=crlf
*.conf text eol=lf
*.toml text eol=lf
*.yaml text eol=lf
*.yml text eol=lf

# License and documentation
LICENSE text eol=lf
CHANGELOG* text eol=lf
CONTRIBUTING* text eol=lf
INSTALL* text eol=lf
NEWS* text eol=lf
README* text eol=lf
TODO* text eol=lf

# Git files
.gitignore text eol=lf
.gitattributes text eol=lf
.gitmodules text eol=lf

# Editor config
.editorconfig text eol=lf

# Linguist overrides for generated files
*.Designer.cs linguist-generated=true
*.designer.cs linguist-generated=true
*Reference.cs linguist-generated=true
*Service.cs linguist-generated=true
*.g.cs linguist-generated=true
*.g.i.cs linguist-generated=true
*.AssemblyInfo.cs linguist-generated=true
GlobalAssemblyInfo.cs linguist-generated=true

# Treat certain paths as documentation
docs/** linguist-documentation=true
*.md linguist-documentation=true
