using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using SkiaSharp;
using OpenTK.Graphics.OpenGL4;
using OpenTK.Windowing.Desktop;

namespace SkiaSample
{
    public class ImageStitcher : IDisposable
    {
        private bool disposed = false;
        private GRContext? grContext;
        private GameWindow? glWindow;

        public enum AspectRatio
        {
            Auto,       // 自动
            Square,     // 1:1
            Standard,   // 4:3
            Classic,    // 3:2
            Widescreen, // 16:9
            Widescreen16_10, // 16:10
            UltraWide   // 21:9
        }

        /// <summary>
        /// 初始化GPU上下文（如果可用）
        /// </summary>
        public void InitializeGpuContext()
        {
            try
            {
                // 创建一个隐藏的OpenGL窗口用于GPU上下文
                var gameWindowSettings = GameWindowSettings.Default;
                var nativeWindowSettings = new NativeWindowSettings()
                {
                    ClientSize = new OpenTK.Mathematics.Vector2i(1, 1),
                    Title = "Hidden GPU Context",
                    WindowBorder = OpenTK.Windowing.Common.WindowBorder.Hidden
                };

                glWindow = new GameWindow(gameWindowSettings, nativeWindowSettings);
                glWindow.IsVisible = false;
                glWindow.MakeCurrent();

                // 创建SkiaSharp的GPU上下文
                var glInterface = GRGlInterface.Create();
                grContext = GRContext.CreateGl(glInterface);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GPU上下文初始化失败: {ex.Message}");
                // 如果GPU初始化失败，将使用CPU渲染
                grContext = null;
                glWindow?.Dispose();
                glWindow = null;
            }
        }

        public class StitchResult
        {
            public SKBitmap? StitchedImage { get; set; }
            public int ActualRows { get; set; }
            public int ActualColumns { get; set; }
            public int TotalImages { get; set; }
            public string Message { get; set; } = string.Empty;
            public float ActualAspectRatio { get; set; }
            public int OutputWidth { get; set; }
            public int OutputHeight { get; set; }

            // 性能信息
            public long LoadingTimeMs { get; set; }
            public long ProcessingTimeMs { get; set; }
            public long TotalTimeMs { get; set; }
            public string PerformanceDetails { get; set; } = string.Empty;
        }

        private static float GetAspectRatioValue(AspectRatio ratio)
        {
            return ratio switch
            {
                AspectRatio.Square => 1.0f,
                AspectRatio.Standard => 4.0f / 3.0f,
                AspectRatio.Classic => 3.0f / 2.0f,
                AspectRatio.Widescreen => 16.0f / 9.0f,
                AspectRatio.Widescreen16_10 => 16.0f / 10.0f,
                AspectRatio.UltraWide => 21.0f / 9.0f,
                _ => 0.0f // Auto
            };
        }

        /// <summary>
        /// GPU加速的图片拼接方法
        /// </summary>
        /// <param name="imagePaths">图片路径列表</param>
        /// <param name="rows">行数</param>
        /// <param name="columns">列数</param>
        /// <param name="aspectRatio">输出比例</param>
        /// <param name="backgroundColor">背景颜色</param>
        /// <returns>拼接结果</returns>
        public StitchResult StitchWithGpuAcceleration(
            List<string> imagePaths,
            int rows,
            int columns,
            AspectRatio aspectRatio = AspectRatio.Auto,
            SKColor? backgroundColor = null)
        {
            // 使用优化渲染路径，偏好GPU
            return StitchWithOptimizedRendering(imagePaths, rows, columns, aspectRatio, backgroundColor, true);
        }

        /// <summary>
        /// CPU渲染的图片拼接方法
        /// </summary>
        /// <param name="imagePaths">图片路径列表</param>
        /// <param name="rows">行数</param>
        /// <param name="columns">列数</param>
        /// <param name="aspectRatio">输出比例</param>
        /// <param name="backgroundColor">背景颜色</param>
        /// <returns>拼接结果</returns>
        public StitchResult StitchWithCpuRendering(
            List<string> imagePaths,
            int rows,
            int columns,
            AspectRatio aspectRatio = AspectRatio.Auto,
            SKColor? backgroundColor = null)
        {
            // 使用优化渲染路径，强制CPU渲染
            return StitchWithOptimizedRendering(imagePaths, rows, columns, aspectRatio, backgroundColor, false);
        }

        /// <summary>
        /// 优化的图片拼接方法，支持GPU/CPU选择
        /// </summary>
        private StitchResult StitchWithOptimizedRendering(
            List<string> imagePaths,
            int rows,
            int columns,
            AspectRatio aspectRatio,
            SKColor? backgroundColor,
            bool preferGpu)
        {
            if (imagePaths == null || imagePaths.Count == 0)
            {
                return new StitchResult { Message = "没有提供图片文件" };
            }

            if (rows <= 0 || columns <= 0)
            {
                return new StitchResult { Message = "行数和列数必须大于0" };
            }

            var result = new StitchResult();
            var totalStopwatch = System.Diagnostics.Stopwatch.StartNew();
            var loadedImages = new List<SKBitmap>();
            var loadingStopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                // 加载所有图片
                foreach (string path in imagePaths)
                {
                    if (File.Exists(path))
                    {
                        try
                        {
                            using FileStream stream = File.OpenRead(path);
                            SKBitmap? bitmap = SKBitmap.Decode(stream);
                            if (bitmap != null)
                            {
                                loadedImages.Add(bitmap);
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"无法加载图片 {path}: {ex.Message}");
                        }
                    }
                }

                loadingStopwatch.Stop();
                result.LoadingTimeMs = loadingStopwatch.ElapsedMilliseconds;

                if (loadedImages.Count == 0)
                {
                    result.Message = "没有成功加载任何图片";
                    return result;
                }

                // 开始处理计时
                var processingStopwatch = System.Diagnostics.Stopwatch.StartNew();

                // 计算输出尺寸
                int maxWidth = loadedImages.Max(img => img.Width);
                int maxHeight = loadedImages.Max(img => img.Height);
                int baseWidth = columns * maxWidth;
                int baseHeight = rows * maxHeight;

                int outputWidth, outputHeight;
                CalculateOutputDimensions(aspectRatio, baseWidth, baseHeight, out outputWidth, out outputHeight);

                result.OutputWidth = outputWidth;
                result.OutputHeight = outputHeight;
                result.ActualAspectRatio = (float)outputWidth / outputHeight;

                // 创建表面 - 让SkiaSharp自动选择最佳后端
                var imageInfo = new SKImageInfo(outputWidth, outputHeight, SKColorType.Rgba8888, SKAlphaType.Premul);

                SKSurface? surface;
                string renderMode;

                if (preferGpu)
                {
                    // 尝试创建GPU表面，如果失败则回退到CPU
                    surface = SKSurface.Create(imageInfo);
                    renderMode = "GPU加速";
                }
                else
                {
                    // 直接创建CPU表面
                    surface = SKSurface.Create(imageInfo);
                    renderMode = "CPU渲染";
                }

                if (surface == null)
                {
                    result.Message = "无法创建渲染表面";
                    return result;
                }

                using (surface)
                {
                    SKCanvas canvas = surface.Canvas;

                    // 设置背景色
                    SKColor bgColor = backgroundColor ?? SKColors.White;
                    canvas.Clear(bgColor);

                    // 计算图片网格在画布中的位置（居中）
                    int gridWidth = columns * maxWidth;
                    int gridHeight = rows * maxHeight;
                    int offsetX = (outputWidth - gridWidth) / 2;
                    int offsetY = (outputHeight - gridHeight) / 2;

                    // 拼接图片
                    int imageIndex = 0;
                    for (int row = 0; row < rows && imageIndex < loadedImages.Count; row++)
                    {
                        for (int col = 0; col < columns && imageIndex < loadedImages.Count; col++)
                        {
                            SKBitmap currentImage = loadedImages[imageIndex];

                            // 计算图片在网格中的位置
                            int cellX = col * maxWidth;
                            int cellY = row * maxHeight;

                            // 计算图片在单元格中的居中位置
                            int imgOffsetX = (maxWidth - currentImage.Width) / 2;
                            int imgOffsetY = (maxHeight - currentImage.Height) / 2;

                            // 最终位置
                            int finalX = offsetX + cellX + imgOffsetX;
                            int finalY = offsetY + cellY + imgOffsetY;

                            // 绘制原始大小的图片
                            canvas.DrawBitmap(currentImage, finalX, finalY);
                            imageIndex++;
                        }
                    }

                    // 完成渲染
                    canvas.Flush();

                    // 从表面获取结果
                    using SKImage image = surface.Snapshot();
                    result.StitchedImage = SKBitmap.FromImage(image);

                    processingStopwatch.Stop();
                    result.ProcessingTimeMs = processingStopwatch.ElapsedMilliseconds;

                    totalStopwatch.Stop();
                    result.TotalTimeMs = totalStopwatch.ElapsedMilliseconds;

                    result.TotalImages = imageIndex;
                    result.ActualRows = rows;
                    result.ActualColumns = columns;
                    result.Message = $"{renderMode}拼接成功: {imageIndex} 张图片 ({rows}x{columns})，输出比例 {result.ActualAspectRatio:F2}:1";
                    result.PerformanceDetails = $"{renderMode} - 加载: {result.LoadingTimeMs}ms, 处理: {result.ProcessingTimeMs}ms, 总计: {result.TotalTimeMs}ms";

                    return result;
                }
            }
            catch (Exception ex)
            {
                totalStopwatch.Stop();
                result.TotalTimeMs = totalStopwatch.ElapsedMilliseconds;
                result.Message = $"拼接过程中出错: {ex.Message}";
                return result;
            }
            finally
            {
                // 释放加载的图片资源
                foreach (SKBitmap img in loadedImages)
                {
                    img?.Dispose();
                }
            }
        }

        /// <summary>
        /// 计算输出尺寸的辅助方法
        /// </summary>
        private static void CalculateOutputDimensions(AspectRatio aspectRatio, int baseWidth, int baseHeight, out int outputWidth, out int outputHeight)
        {
            if (aspectRatio == AspectRatio.Auto)
            {
                outputWidth = baseWidth;
                outputHeight = baseHeight;
                return;
            }

            float targetRatio = aspectRatio switch
            {
                AspectRatio.Square => 1.0f,
                AspectRatio.Standard => 4.0f / 3.0f,
                AspectRatio.Classic => 3.0f / 2.0f,
                AspectRatio.Widescreen => 16.0f / 9.0f,
                AspectRatio.Widescreen16_10 => 16.0f / 10.0f,
                AspectRatio.UltraWide => 21.0f / 9.0f,
                _ => (float)baseWidth / baseHeight
            };

            float currentRatio = (float)baseWidth / baseHeight;

            if (currentRatio > targetRatio)
            {
                // 当前比例太宽，以高度为准
                outputHeight = baseHeight;
                outputWidth = (int)(baseHeight * targetRatio);
            }
            else
            {
                // 当前比例太窄，以宽度为准
                outputWidth = baseWidth;
                outputHeight = (int)(baseWidth / targetRatio);
            }
        }

        /// <summary>
        /// 拼接多张图片为一张图片
        /// </summary>
        /// <param name="imagePaths">图片文件路径列表</param>
        /// <param name="rows">行数</param>
        /// <param name="columns">列数</param>
        /// <param name="cellWidth">每个单元格的宽度（0表示自动计算）</param>
        /// <param name="cellHeight">每个单元格的高度（0表示自动计算）</param>
        /// <param name="backgroundColor">背景颜色</param>
        /// <returns>拼接结果</returns>
        public StitchResult StitchImages(
            List<string> imagePaths, 
            int rows, 
            int columns, 
            int cellWidth = 0, 
            int cellHeight = 0,
            SKColor? backgroundColor = null)
        {
            var result = new StitchResult();
            
            if (imagePaths == null || imagePaths.Count == 0)
            {
                result.Message = "没有提供图片文件";
                return result;
            }

            if (rows <= 0 || columns <= 0)
            {
                result.Message = "行数和列数必须大于0";
                return result;
            }

            var loadedImages = new List<SKBitmap>();
            
            try
            {
                // 加载所有图片
                foreach (var path in imagePaths)
                {
                    if (File.Exists(path))
                    {
                        try
                        {
                            using var stream = File.OpenRead(path);
                            var bitmap = SKBitmap.Decode(stream);
                            if (bitmap != null)
                            {
                                loadedImages.Add(bitmap);
                            }
                        }
                        catch (Exception ex)
                        {
                            // 跳过无法加载的图片
                            Console.WriteLine($"无法加载图片 {path}: {ex.Message}");
                        }
                    }
                }

                if (loadedImages.Count == 0)
                {
                    result.Message = "没有成功加载任何图片";
                    return result;
                }

                // 计算实际需要的行列数
                int totalCells = rows * columns;
                int actualImages = Math.Min(loadedImages.Count, totalCells);
                
                result.TotalImages = actualImages;
                result.ActualRows = rows;
                result.ActualColumns = columns;

                // 如果没有指定单元格大小，则根据图片计算
                if (cellWidth <= 0 || cellHeight <= 0)
                {
                    var avgWidth = (int)loadedImages.Average(img => img.Width);
                    var avgHeight = (int)loadedImages.Average(img => img.Height);
                    
                    if (cellWidth <= 0) cellWidth = avgWidth;
                    if (cellHeight <= 0) cellHeight = avgHeight;
                }

                // 创建拼接后的图片
                int totalWidth = columns * cellWidth;
                int totalHeight = rows * cellHeight;
                
                var stitchedBitmap = new SKBitmap(totalWidth, totalHeight);
                using var canvas = new SKCanvas(stitchedBitmap);
                
                // 设置背景色
                var bgColor = backgroundColor ?? SKColors.White;
                canvas.Clear(bgColor);

                // 拼接图片
                int imageIndex = 0;
                for (int row = 0; row < rows && imageIndex < actualImages; row++)
                {
                    for (int col = 0; col < columns && imageIndex < actualImages; col++)
                    {
                        var currentImage = loadedImages[imageIndex];
                        
                        // 计算目标位置
                        int destX = col * cellWidth;
                        int destY = row * cellHeight;
                        
                        // 计算缩放比例以适应单元格
                        float scaleX = (float)cellWidth / currentImage.Width;
                        float scaleY = (float)cellHeight / currentImage.Height;
                        float scale = Math.Min(scaleX, scaleY);
                        
                        int scaledWidth = (int)(currentImage.Width * scale);
                        int scaledHeight = (int)(currentImage.Height * scale);
                        
                        // 居中显示
                        int offsetX = (cellWidth - scaledWidth) / 2;
                        int offsetY = (cellHeight - scaledHeight) / 2;
                        
                        var destRect = new SKRect(
                            destX + offsetX, 
                            destY + offsetY, 
                            destX + offsetX + scaledWidth, 
                            destY + offsetY + scaledHeight);
                        
                        canvas.DrawBitmap(currentImage, destRect);
                        imageIndex++;
                    }
                }

                result.StitchedImage = stitchedBitmap;
                result.Message = $"成功拼接 {actualImages} 张图片 ({rows}x{columns})";
                
                return result;
            }
            catch (Exception ex)
            {
                result.Message = $"拼接过程中出错: {ex.Message}";
                return result;
            }
            finally
            {
                // 释放加载的图片资源
                foreach (var img in loadedImages)
                {
                    img?.Dispose();
                }
            }
        }

        /// <summary>
        /// 创建网格布局的拼接图片
        /// </summary>
        /// <param name="imagePaths">图片路径列表</param>
        /// <param name="rows">行数</param>
        /// <param name="columns">列数</param>
        /// <param name="maxCellSize">最大单元格尺寸</param>
        /// <param name="padding">单元格间距</param>
        /// <returns>拼接结果</returns>
        public StitchResult CreateGridLayout(
            List<string> imagePaths, 
            int rows, 
            int columns, 
            int maxCellSize = 300, 
            int padding = 5)
        {
            var result = new StitchResult();
            
            if (imagePaths == null || imagePaths.Count == 0)
            {
                result.Message = "没有提供图片文件";
                return result;
            }

            var loadedImages = new List<SKBitmap>();
            
            try
            {
                // 加载图片
                foreach (var path in imagePaths.Take(rows * columns))
                {
                    if (File.Exists(path))
                    {
                        try
                        {
                            using var stream = File.OpenRead(path);
                            var bitmap = SKBitmap.Decode(stream);
                            if (bitmap != null)
                            {
                                loadedImages.Add(bitmap);
                            }
                        }
                        catch
                        {
                            // 跳过无法加载的图片
                        }
                    }
                }

                if (loadedImages.Count == 0)
                {
                    result.Message = "没有成功加载任何图片";
                    return result;
                }

                // 计算网格尺寸
                int cellSize = Math.Min(maxCellSize, 
                    Math.Min(
                        loadedImages.Max(img => img.Width),
                        loadedImages.Max(img => img.Height)
                    ));

                int totalWidth = columns * cellSize + (columns - 1) * padding;
                int totalHeight = rows * cellSize + (rows - 1) * padding;

                var stitchedBitmap = new SKBitmap(totalWidth, totalHeight);
                using var canvas = new SKCanvas(stitchedBitmap);
                canvas.Clear(SKColors.LightGray);

                // 绘制网格
                int imageIndex = 0;
                for (int row = 0; row < rows && imageIndex < loadedImages.Count; row++)
                {
                    for (int col = 0; col < columns && imageIndex < loadedImages.Count; col++)
                    {
                        var currentImage = loadedImages[imageIndex];
                        
                        int destX = col * (cellSize + padding);
                        int destY = row * (cellSize + padding);
                        
                        // 绘制白色背景
                        using var bgPaint = new SKPaint { Color = SKColors.White };
                        canvas.DrawRect(destX, destY, cellSize, cellSize, bgPaint);
                        
                        // 计算图片缩放
                        float scale = Math.Min(
                            (float)cellSize / currentImage.Width,
                            (float)cellSize / currentImage.Height);
                        
                        int scaledWidth = (int)(currentImage.Width * scale);
                        int scaledHeight = (int)(currentImage.Height * scale);
                        
                        int offsetX = (cellSize - scaledWidth) / 2;
                        int offsetY = (cellSize - scaledHeight) / 2;
                        
                        var destRect = new SKRect(
                            destX + offsetX,
                            destY + offsetY,
                            destX + offsetX + scaledWidth,
                            destY + offsetY + scaledHeight);
                        
                        canvas.DrawBitmap(currentImage, destRect);
                        imageIndex++;
                    }
                }

                result.StitchedImage = stitchedBitmap;
                result.ActualRows = rows;
                result.ActualColumns = columns;
                result.TotalImages = imageIndex;
                result.Message = $"成功创建网格布局 {imageIndex} 张图片";
                
                return result;
            }
            catch (Exception ex)
            {
                result.Message = $"创建网格布局时出错: {ex.Message}";
                return result;
            }
            finally
            {
                foreach (var img in loadedImages)
                {
                    img?.Dispose();
                }
            }
        }

        /// <summary>
        /// 按指定比例拼接图片，保持原图片大小不变
        /// </summary>
        /// <param name="imagePaths">图片路径列表</param>
        /// <param name="rows">行数</param>
        /// <param name="columns">列数</param>
        /// <param name="aspectRatio">输出比例</param>
        /// <param name="backgroundColor">背景颜色</param>
        /// <returns>拼接结果</returns>
        public StitchResult StitchWithAspectRatio(
            List<string> imagePaths,
            int rows,
            int columns,
            AspectRatio aspectRatio = AspectRatio.Auto,
            SKColor? backgroundColor = null)
        {
            var result = new StitchResult();
            var totalStopwatch = System.Diagnostics.Stopwatch.StartNew();

            if (imagePaths == null || imagePaths.Count == 0)
            {
                result.Message = "没有提供图片文件";
                return result;
            }

            if (rows <= 0 || columns <= 0)
            {
                result.Message = "行数和列数必须大于0";
                return result;
            }

            var loadedImages = new List<SKBitmap>();
            var loadingStopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                // 加载所有图片
                foreach (var path in imagePaths)
                {
                    if (File.Exists(path))
                    {
                        try
                        {
                            using var stream = File.OpenRead(path);
                            var bitmap = SKBitmap.Decode(stream);
                            if (bitmap != null)
                            {
                                loadedImages.Add(bitmap);
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"无法加载图片 {path}: {ex.Message}");
                        }
                    }
                }

                // 停止加载计时
                loadingStopwatch.Stop();
                result.LoadingTimeMs = loadingStopwatch.ElapsedMilliseconds;

                if (loadedImages.Count == 0)
                {
                    result.Message = "没有成功加载任何图片";
                    totalStopwatch.Stop();
                    result.TotalTimeMs = totalStopwatch.ElapsedMilliseconds;
                    return result;
                }

                // 开始处理计时
                var processingStopwatch = System.Diagnostics.Stopwatch.StartNew();

                // 计算实际需要的图片数量
                int totalCells = rows * columns;
                int actualImages = Math.Min(loadedImages.Count, totalCells);

                result.TotalImages = actualImages;
                result.ActualRows = rows;
                result.ActualColumns = columns;

                // 计算每个单元格的大小（保持原图片大小）
                int maxWidth = loadedImages.Max(img => img.Width);
                int maxHeight = loadedImages.Max(img => img.Height);

                // 计算基础画布大小
                int baseWidth = columns * maxWidth;
                int baseHeight = rows * maxHeight;

                // 根据指定比例调整输出尺寸
                int outputWidth, outputHeight;

                if (aspectRatio == AspectRatio.Auto)
                {
                    // 自动模式：使用基础尺寸
                    outputWidth = baseWidth;
                    outputHeight = baseHeight;
                }
                else
                {
                    // 按指定比例调整
                    float targetRatio = GetAspectRatioValue(aspectRatio);
                    float currentRatio = (float)baseWidth / baseHeight;

                    if (currentRatio > targetRatio)
                    {
                        // 当前比例太宽，以高度为准
                        outputHeight = baseHeight;
                        outputWidth = (int)(outputHeight * targetRatio);
                    }
                    else
                    {
                        // 当前比例太窄，以宽度为准
                        outputWidth = baseWidth;
                        outputHeight = (int)(outputWidth / targetRatio);
                    }
                }

                result.OutputWidth = outputWidth;
                result.OutputHeight = outputHeight;
                result.ActualAspectRatio = (float)outputWidth / outputHeight;

                // 创建输出画布
                var stitchedBitmap = new SKBitmap(outputWidth, outputHeight);
                using var canvas = new SKCanvas(stitchedBitmap);

                // 设置背景色
                var bgColor = backgroundColor ?? SKColors.White;
                canvas.Clear(bgColor);

                // 计算图片网格在画布中的位置（居中）
                int gridWidth = columns * maxWidth;
                int gridHeight = rows * maxHeight;
                int offsetX = (outputWidth - gridWidth) / 2;
                int offsetY = (outputHeight - gridHeight) / 2;

                // 拼接图片
                int imageIndex = 0;
                for (int row = 0; row < rows && imageIndex < actualImages; row++)
                {
                    for (int col = 0; col < columns && imageIndex < actualImages; col++)
                    {
                        var currentImage = loadedImages[imageIndex];

                        // 计算图片在网格中的位置
                        int cellX = col * maxWidth;
                        int cellY = row * maxHeight;

                        // 计算图片在单元格中的居中位置
                        int imgOffsetX = (maxWidth - currentImage.Width) / 2;
                        int imgOffsetY = (maxHeight - currentImage.Height) / 2;

                        // 最终位置
                        int finalX = offsetX + cellX + imgOffsetX;
                        int finalY = offsetY + cellY + imgOffsetY;

                        // 绘制原始大小的图片
                        canvas.DrawBitmap(currentImage, finalX, finalY);
                        imageIndex++;
                    }
                }

                // 停止处理计时
                processingStopwatch.Stop();
                result.ProcessingTimeMs = processingStopwatch.ElapsedMilliseconds;

                // 停止总计时
                totalStopwatch.Stop();
                result.TotalTimeMs = totalStopwatch.ElapsedMilliseconds;

                result.StitchedImage = stitchedBitmap;
                result.Message = $"成功拼接 {actualImages} 张图片 ({rows}x{columns})，输出比例 {result.ActualAspectRatio:F2}:1";

                // 生成性能详情
                result.PerformanceDetails = $"加载: {result.LoadingTimeMs}ms, 处理: {result.ProcessingTimeMs}ms, 总计: {result.TotalTimeMs}ms";

                return result;
            }
            catch (Exception ex)
            {
                // 停止总计时
                totalStopwatch.Stop();
                result.TotalTimeMs = totalStopwatch.ElapsedMilliseconds;
                result.PerformanceDetails = $"总计: {result.TotalTimeMs}ms (出错)";
                result.Message = $"拼接过程中出错: {ex.Message}";
                return result;
            }
            finally
            {
                // 释放加载的图片资源
                foreach (var img in loadedImages)
                {
                    img?.Dispose();
                }
            }
        }

        /// <summary>
        /// 性能测试：比较 SkiaSharp 和 GDI+ 的拼接性能
        /// </summary>
        /// <param name="imagePaths">测试图片路径</param>
        /// <param name="rows">行数</param>
        /// <param name="columns">列数</param>
        /// <param name="aspectRatio">输出比例</param>
        /// <returns>性能测试结果</returns>
        public static (TimeSpan SkiaTime, TimeSpan GdiTime, string Report) BenchmarkStitching(
            List<string> imagePaths, int rows, int columns, AspectRatio aspectRatio = AspectRatio.Auto)
        {
            var report = new System.Text.StringBuilder();

            // SkiaSharp 性能测试
            var skiaStopwatch = System.Diagnostics.Stopwatch.StartNew();
            StitchResult? skiaResult = null;

            try
            {
                using var stitcher = new ImageStitcher();
                skiaResult = stitcher.StitchWithAspectRatio(imagePaths, rows, columns, aspectRatio);
                skiaStopwatch.Stop();

                var aspectRatioText = aspectRatio == AspectRatio.Auto ? "自动" : aspectRatio.ToString();
                report.AppendLine($"SkiaSharp 拼接 (比例: {aspectRatioText}):");
                report.AppendLine($"  时间: {skiaStopwatch.ElapsedMilliseconds} ms");
                report.AppendLine($"  结果: {(skiaResult.StitchedImage != null ? "成功" : "失败")}");
                if (skiaResult.StitchedImage != null)
                {
                    report.AppendLine($"  输出尺寸: {skiaResult.OutputWidth}x{skiaResult.OutputHeight}");
                    report.AppendLine($"  实际比例: {skiaResult.ActualAspectRatio:F2}:1");
                    report.AppendLine($"  性能详情: {skiaResult.PerformanceDetails}");
                }
            }
            catch (Exception ex)
            {
                skiaStopwatch.Stop();
                report.AppendLine($"SkiaSharp 拼接失败: {ex.Message}");
            }

            // GDI+ 性能测试
            var gdiStopwatch = System.Diagnostics.Stopwatch.StartNew();
            System.Drawing.Bitmap? gdiResult = null;

            try
            {
                gdiResult = StitchWithGDI(imagePaths, rows, columns, aspectRatio);
                gdiStopwatch.Stop();

                var aspectRatioText = aspectRatio == AspectRatio.Auto ? "自动" : aspectRatio.ToString();
                report.AppendLine($"\nGDI+ 拼接 (比例: {aspectRatioText}):");
                report.AppendLine($"  时间: {gdiStopwatch.ElapsedMilliseconds} ms");
                report.AppendLine($"  结果: {(gdiResult != null ? "成功" : "失败")}");
                if (gdiResult != null)
                {
                    report.AppendLine($"  输出尺寸: {gdiResult.Width}x{gdiResult.Height}");
                    var actualRatio = (float)gdiResult.Width / gdiResult.Height;
                    report.AppendLine($"  实际比例: {actualRatio:F2}:1");
                }
            }
            catch (Exception ex)
            {
                gdiStopwatch.Stop();
                report.AppendLine($"\nGDI+ 拼接失败: {ex.Message}");
            }

            // 性能对比
            if (skiaStopwatch.ElapsedMilliseconds > 0 && gdiStopwatch.ElapsedMilliseconds > 0)
            {
                var speedup = (double)gdiStopwatch.ElapsedMilliseconds / skiaStopwatch.ElapsedMilliseconds;
                report.AppendLine($"\n性能对比:");
                report.AppendLine($"  SkiaSharp 比 GDI+ 快 {speedup:F2}x");
            }

            // 清理资源
            skiaResult?.StitchedImage?.Dispose();
            gdiResult?.Dispose();

            return (skiaStopwatch.Elapsed, gdiStopwatch.Elapsed, report.ToString());
        }

        /// <summary>
        /// 使用 GDI+ 进行图片拼接（用于性能对比）
        /// </summary>
        private static System.Drawing.Bitmap? StitchWithGDI(List<string> imagePaths, int rows, int columns, AspectRatio aspectRatio = AspectRatio.Auto)
        {
            var loadedImages = new List<System.Drawing.Bitmap>();

            try
            {
                // 加载图片
                foreach (var path in imagePaths.Take(rows * columns))
                {
                    if (File.Exists(path))
                    {
                        try
                        {
                            var bitmap = new System.Drawing.Bitmap(path);
                            loadedImages.Add(bitmap);
                        }
                        catch
                        {
                            // 跳过无法加载的图片
                        }
                    }
                }

                if (loadedImages.Count == 0) return null;

                // 计算每个单元格的大小（保持原图片大小）
                int maxWidth = loadedImages.Max(img => img.Width);
                int maxHeight = loadedImages.Max(img => img.Height);

                // 计算基础画布大小
                int baseWidth = columns * maxWidth;
                int baseHeight = rows * maxHeight;

                // 根据指定比例调整输出尺寸
                int outputWidth, outputHeight;

                if (aspectRatio == AspectRatio.Auto)
                {
                    // 自动模式：使用基础尺寸
                    outputWidth = baseWidth;
                    outputHeight = baseHeight;
                }
                else
                {
                    // 按指定比例调整
                    float targetRatio = GetAspectRatioValue(aspectRatio);
                    float currentRatio = (float)baseWidth / baseHeight;

                    if (currentRatio > targetRatio)
                    {
                        // 当前比例太宽，以高度为准
                        outputHeight = baseHeight;
                        outputWidth = (int)(outputHeight * targetRatio);
                    }
                    else
                    {
                        // 当前比例太窄，以宽度为准
                        outputWidth = baseWidth;
                        outputHeight = (int)(outputWidth / targetRatio);
                    }
                }

                // 创建输出图片
                var result = new System.Drawing.Bitmap(outputWidth, outputHeight);
                using var graphics = System.Drawing.Graphics.FromImage(result);

                graphics.Clear(System.Drawing.Color.White);
                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;

                // 计算图片网格在画布中的位置（居中）
                int gridWidth = columns * maxWidth;
                int gridHeight = rows * maxHeight;
                int offsetX = (outputWidth - gridWidth) / 2;
                int offsetY = (outputHeight - gridHeight) / 2;

                // 拼接图片
                int imageIndex = 0;
                for (int row = 0; row < rows && imageIndex < loadedImages.Count; row++)
                {
                    for (int col = 0; col < columns && imageIndex < loadedImages.Count; col++)
                    {
                        var currentImage = loadedImages[imageIndex];

                        // 计算图片在网格中的位置
                        int cellX = col * maxWidth;
                        int cellY = row * maxHeight;

                        // 计算图片在单元格中的居中位置
                        int imgOffsetX = (maxWidth - currentImage.Width) / 2;
                        int imgOffsetY = (maxHeight - currentImage.Height) / 2;

                        // 最终位置
                        int finalX = offsetX + cellX + imgOffsetX;
                        int finalY = offsetY + cellY + imgOffsetY;

                        // 绘制原始大小的图片
                        graphics.DrawImage(currentImage, finalX, finalY);
                        imageIndex++;
                    }
                }

                return result;
            }
            finally
            {
                foreach (var img in loadedImages)
                {
                    img?.Dispose();
                }
            }
        }

        public void Dispose()
        {
            if (!disposed)
            {
                // 清理GPU资源
                grContext?.Dispose();
                glWindow?.Dispose();
                disposed = true;
            }
            GC.SuppressFinalize(this);
        }
    }
}
