namespace SkiaSample
{
    partial class MainForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            rightPanel = new Panel();
            bottomPanel = new Panel();
            pictureBox = new PictureBox();
            groupBoxEffects = new GroupBox();
            btnLoadImage = new Button();
            lblEffect = new Label();
            cmbEffects = new ComboBox();
            lblIntensity = new Label();
            trackBarIntensity = new TrackBar();
            btnApplyEffect = new Button();
            btnSaveImage = new Button();
            chkGpuAcceleration = new CheckBox();
            lblStatus = new Label();
            groupBoxStitch = new GroupBox();
            btnBenchmark = new Button();
            cmbAspectRatio = new ComboBox();
            lblAspectRatio = new Label();
            listBoxImages = new ListBox();
            btnStitchImages = new Button();
            numericUpDownColumns = new NumericUpDown();
            lblColumns = new Label();
            numericUpDownRows = new NumericUpDown();
            lblRows = new Label();
            btnLoadMultipleImages = new Button();
            rightPanel.SuspendLayout();
            bottomPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox).BeginInit();
            groupBoxEffects.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)trackBarIntensity).BeginInit();
            groupBoxStitch.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDownColumns).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numericUpDownRows).BeginInit();
            SuspendLayout();
            //
            // rightPanel
            //
            rightPanel.Dock = DockStyle.Right;
            rightPanel.Location = new Point(620, 0);
            rightPanel.Name = "rightPanel";
            rightPanel.Size = new Size(280, 620);
            rightPanel.TabIndex = 0;
            rightPanel.Controls.Add(groupBoxStitch);
            rightPanel.Controls.Add(groupBoxEffects);
            //
            // bottomPanel
            //
            bottomPanel.Dock = DockStyle.Bottom;
            bottomPanel.Location = new Point(0, 620);
            bottomPanel.Name = "bottomPanel";
            bottomPanel.Size = new Size(900, 40);
            bottomPanel.TabIndex = 1;
            bottomPanel.Controls.Add(lblStatus);
            //
            // pictureBox
            //
            pictureBox.Dock = DockStyle.Fill;
            pictureBox.BackColor = Color.White;
            pictureBox.BorderStyle = BorderStyle.FixedSingle;
            pictureBox.Name = "pictureBox";
            pictureBox.SizeMode = PictureBoxSizeMode.Zoom;
            pictureBox.TabIndex = 2;
            pictureBox.TabStop = false;
            //
            // groupBoxEffects
            //
            groupBoxEffects.Controls.Add(chkGpuAcceleration);
            groupBoxEffects.Controls.Add(btnSaveImage);
            groupBoxEffects.Controls.Add(btnApplyEffect);
            groupBoxEffects.Controls.Add(trackBarIntensity);
            groupBoxEffects.Controls.Add(lblIntensity);
            groupBoxEffects.Controls.Add(cmbEffects);
            groupBoxEffects.Controls.Add(lblEffect);
            groupBoxEffects.Controls.Add(btnLoadImage);
            groupBoxEffects.Location = new Point(10, 10);
            groupBoxEffects.Name = "groupBoxEffects";
            groupBoxEffects.Size = new Size(260, 280);
            groupBoxEffects.TabIndex = 0;
            groupBoxEffects.TabStop = false;
            groupBoxEffects.Text = "图像效果";
            //
            // btnLoadImage
            //
            btnLoadImage.Location = new Point(10, 25);
            btnLoadImage.Name = "btnLoadImage";
            btnLoadImage.Size = new Size(100, 30);
            btnLoadImage.TabIndex = 0;
            btnLoadImage.Text = "加载图片";
            btnLoadImage.UseVisualStyleBackColor = true;
            btnLoadImage.Click += BtnLoadImage_Click;
            //
            // lblEffect
            //
            lblEffect.AutoSize = true;
            lblEffect.Location = new Point(10, 65);
            lblEffect.Name = "lblEffect";
            lblEffect.Size = new Size(59, 17);
            lblEffect.TabIndex = 1;
            lblEffect.Text = "选择效果:";
            //
            // cmbEffects
            //
            cmbEffects.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbEffects.FormattingEnabled = true;
            cmbEffects.Items.AddRange(new object[] { "模糊", "亮度调整", "对比度调整", "锐化", "浮雕效果" });
            cmbEffects.Location = new Point(10, 85);
            cmbEffects.Name = "cmbEffects";
            cmbEffects.Size = new Size(240, 25);
            cmbEffects.TabIndex = 2;
            //
            // lblIntensity
            //
            lblIntensity.AutoSize = true;
            lblIntensity.Location = new Point(10, 125);
            lblIntensity.Name = "lblIntensity";
            lblIntensity.Size = new Size(64, 17);
            lblIntensity.TabIndex = 3;
            lblIntensity.Text = "强度: 50%";
            //
            // trackBarIntensity
            //
            trackBarIntensity.Location = new Point(10, 145);
            trackBarIntensity.Maximum = 100;
            trackBarIntensity.Name = "trackBarIntensity";
            trackBarIntensity.Size = new Size(240, 45);
            trackBarIntensity.TabIndex = 4;
            trackBarIntensity.TickFrequency = 10;
            trackBarIntensity.Value = 50;
            trackBarIntensity.ValueChanged += TrackBarIntensity_ValueChanged;
            //
            // btnApplyEffect
            //
            btnApplyEffect.Location = new Point(10, 205);
            btnApplyEffect.Name = "btnApplyEffect";
            btnApplyEffect.Size = new Size(100, 30);
            btnApplyEffect.TabIndex = 5;
            btnApplyEffect.Text = "应用效果";
            btnApplyEffect.UseVisualStyleBackColor = true;
            btnApplyEffect.Click += BtnApplyEffect_Click;
            //
            // btnSaveImage
            //
            btnSaveImage.Enabled = false;
            btnSaveImage.Location = new Point(130, 205);
            btnSaveImage.Name = "btnSaveImage";
            btnSaveImage.Size = new Size(100, 30);
            btnSaveImage.TabIndex = 6;
            btnSaveImage.Text = "保存图片";
            btnSaveImage.UseVisualStyleBackColor = true;
            btnSaveImage.Click += BtnSaveImage_Click;
            //
            // chkGpuAcceleration
            //
            chkGpuAcceleration.AutoSize = true;
            chkGpuAcceleration.Checked = true;
            chkGpuAcceleration.CheckState = CheckState.Checked;
            chkGpuAcceleration.Location = new Point(10, 245);
            chkGpuAcceleration.Name = "chkGpuAcceleration";
            chkGpuAcceleration.Size = new Size(99, 21);
            chkGpuAcceleration.TabIndex = 7;
            chkGpuAcceleration.Text = "启用GPU加速";
            chkGpuAcceleration.UseVisualStyleBackColor = true;
            chkGpuAcceleration.CheckedChanged += ChkGpuAcceleration_CheckedChanged;
            //
            // lblStatus
            //
            lblStatus.AutoSize = true;
            lblStatus.Location = new Point(10, 10);
            lblStatus.Name = "lblStatus";
            lblStatus.Size = new Size(32, 17);
            lblStatus.TabIndex = 0;
            lblStatus.Text = "就绪";
            //
            // groupBoxStitch
            //
            groupBoxStitch.Controls.Add(btnBenchmark);
            groupBoxStitch.Controls.Add(cmbAspectRatio);
            groupBoxStitch.Controls.Add(lblAspectRatio);
            groupBoxStitch.Controls.Add(listBoxImages);
            groupBoxStitch.Controls.Add(btnStitchImages);
            groupBoxStitch.Controls.Add(numericUpDownColumns);
            groupBoxStitch.Controls.Add(lblColumns);
            groupBoxStitch.Controls.Add(numericUpDownRows);
            groupBoxStitch.Controls.Add(lblRows);
            groupBoxStitch.Controls.Add(btnLoadMultipleImages);
            groupBoxStitch.Location = new Point(10, 300);
            groupBoxStitch.Name = "groupBoxStitch";
            groupBoxStitch.Size = new Size(260, 310);
            groupBoxStitch.TabIndex = 1;
            groupBoxStitch.TabStop = false;
            groupBoxStitch.Text = "图片拼接";
            // 
            // btnBenchmark
            // 
            btnBenchmark.Enabled = false;
            btnBenchmark.Location = new Point(140, 255);
            btnBenchmark.Name = "btnBenchmark";
            btnBenchmark.Size = new Size(70, 30);
            btnBenchmark.TabIndex = 9;
            btnBenchmark.Text = "性能测试";
            btnBenchmark.UseVisualStyleBackColor = true;
            btnBenchmark.Click += BtnBenchmark_Click;
            //
            // cmbAspectRatio
            //
            cmbAspectRatio.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbAspectRatio.FormattingEnabled = true;
            cmbAspectRatio.Items.AddRange(new object[] { "自动", "1:1 (正方形)", "4:3 (标准)", "3:2 (经典)", "16:9 (宽屏)", "16:10 (宽屏)", "21:9 (超宽屏)" });
            cmbAspectRatio.Location = new Point(10, 220);
            cmbAspectRatio.Name = "cmbAspectRatio";
            cmbAspectRatio.Size = new Size(240, 25);
            cmbAspectRatio.TabIndex = 8;
            // 
            // lblAspectRatio
            // 
            lblAspectRatio.AutoSize = true;
            lblAspectRatio.Location = new Point(10, 200);
            lblAspectRatio.Name = "lblAspectRatio";
            lblAspectRatio.Size = new Size(59, 17);
            lblAspectRatio.TabIndex = 7;
            lblAspectRatio.Text = "输出比例:";
            //
            // listBoxImages
            //
            listBoxImages.FormattingEnabled = true;
            listBoxImages.Location = new Point(10, 60);
            listBoxImages.Name = "listBoxImages";
            listBoxImages.Size = new Size(240, 89);
            listBoxImages.TabIndex = 6;
            // 
            // btnStitchImages
            // 
            btnStitchImages.Enabled = false;
            btnStitchImages.Location = new Point(10, 255);
            btnStitchImages.Name = "btnStitchImages";
            btnStitchImages.Size = new Size(100, 30);
            btnStitchImages.TabIndex = 5;
            btnStitchImages.Text = "开始拼接";
            btnStitchImages.UseVisualStyleBackColor = true;
            btnStitchImages.Click += BtnStitchImages_Click;
            // 
            // numericUpDownColumns
            // 
            numericUpDownColumns.Location = new Point(155, 168);
            numericUpDownColumns.Maximum = new decimal(new int[] { 10, 0, 0, 0 });
            numericUpDownColumns.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numericUpDownColumns.Name = "numericUpDownColumns";
            numericUpDownColumns.Size = new Size(60, 23);
            numericUpDownColumns.TabIndex = 4;
            numericUpDownColumns.Value = new decimal(new int[] { 2, 0, 0, 0 });
            // 
            // lblColumns
            // 
            lblColumns.AutoSize = true;
            lblColumns.Location = new Point(120, 170);
            lblColumns.Name = "lblColumns";
            lblColumns.Size = new Size(35, 17);
            lblColumns.TabIndex = 3;
            lblColumns.Text = "列数:";
            // 
            // numericUpDownRows
            // 
            numericUpDownRows.Location = new Point(50, 168);
            numericUpDownRows.Maximum = new decimal(new int[] { 10, 0, 0, 0 });
            numericUpDownRows.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numericUpDownRows.Name = "numericUpDownRows";
            numericUpDownRows.Size = new Size(60, 23);
            numericUpDownRows.TabIndex = 2;
            numericUpDownRows.Value = new decimal(new int[] { 2, 0, 0, 0 });
            // 
            // lblRows
            // 
            lblRows.AutoSize = true;
            lblRows.Location = new Point(10, 170);
            lblRows.Name = "lblRows";
            lblRows.Size = new Size(35, 17);
            lblRows.TabIndex = 1;
            lblRows.Text = "行数:";
            //
            // btnLoadMultipleImages
            //
            btnLoadMultipleImages.Location = new Point(10, 25);
            btnLoadMultipleImages.Name = "btnLoadMultipleImages";
            btnLoadMultipleImages.Size = new Size(120, 30);
            btnLoadMultipleImages.TabIndex = 0;
            btnLoadMultipleImages.Text = "导入多张图片";
            btnLoadMultipleImages.UseVisualStyleBackColor = true;
            btnLoadMultipleImages.Click += BtnLoadMultipleImages_Click;
            //
            // MainForm
            //
            AutoScaleDimensions = new SizeF(96F, 96F);
            AutoScaleMode = AutoScaleMode.Dpi;
            ClientSize = new Size(900, 660);
            Controls.Add(pictureBox);
            Controls.Add(rightPanel);
            Controls.Add(bottomPanel);
            Name = "MainForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "GPU 加速图像处理测试";
            rightPanel.ResumeLayout(false);
            bottomPanel.ResumeLayout(false);
            bottomPanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox).EndInit();
            groupBoxEffects.ResumeLayout(false);
            groupBoxEffects.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)trackBarIntensity).EndInit();
            groupBoxStitch.ResumeLayout(false);
            groupBoxStitch.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDownColumns).EndInit();
            ((System.ComponentModel.ISupportInitialize)numericUpDownRows).EndInit();
            ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel rightPanel;
        private System.Windows.Forms.Panel bottomPanel;
        private System.Windows.Forms.PictureBox pictureBox;
        private System.Windows.Forms.GroupBox groupBoxEffects;
        private System.Windows.Forms.Button btnLoadImage;
        private System.Windows.Forms.Label lblEffect;
        private System.Windows.Forms.ComboBox cmbEffects;
        private System.Windows.Forms.Label lblIntensity;
        private System.Windows.Forms.TrackBar trackBarIntensity;
        private System.Windows.Forms.Button btnApplyEffect;
        private System.Windows.Forms.Button btnSaveImage;
        private System.Windows.Forms.CheckBox chkGpuAcceleration;
        private System.Windows.Forms.Label lblStatus;
        private System.Windows.Forms.GroupBox groupBoxStitch;
        private System.Windows.Forms.Button btnLoadMultipleImages;
        private System.Windows.Forms.Label lblRows;
        private System.Windows.Forms.NumericUpDown numericUpDownRows;
        private System.Windows.Forms.Label lblColumns;
        private System.Windows.Forms.NumericUpDown numericUpDownColumns;
        private System.Windows.Forms.Button btnStitchImages;
        private System.Windows.Forms.ListBox listBoxImages;
        private System.Windows.Forms.Label lblAspectRatio;
        private System.Windows.Forms.ComboBox cmbAspectRatio;
        private System.Windows.Forms.Button btnBenchmark;
    }
}
