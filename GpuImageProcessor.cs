using System;
using SkiaSharp;

namespace SkiaSample
{
    public class GpuImageProcessor : IDisposable
    {
        private bool disposed = false;
        private GRContext? grContext;

        public GpuImageProcessor()
        {
            // 简化初始化，不再依赖 OpenTK
        }

        /// <summary>
        /// 初始化GPU上下文
        /// </summary>
        public void InitializeGpuContext()
        {
            try
            {
                // 这里可以添加GPU上下文初始化逻辑
                // 目前使用SkiaSharp的自动GPU检测
                grContext = null; // 暂时设为null，让SkiaSharp自动处理
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GPU上下文初始化失败: {ex.Message}");
                grContext = null;
            }
        }

        /// <summary>
        /// 根据用户选择应用图像效果（GPU加速或CPU渲染）
        /// </summary>
        /// <param name="originalBitmap">原始图片</param>
        /// <param name="effectType">效果类型</param>
        /// <param name="intensity">强度</param>
        /// <param name="useGpuAcceleration">是否使用GPU加速</param>
        /// <returns>处理后的图片</returns>
        public SKBitmap ApplyEffect(SKBitmap originalBitmap, string effectType, float intensity, bool useGpuAcceleration = true)
        {
            if (useGpuAcceleration)
            {
                return ApplyEffectWithGpu(originalBitmap, effectType, intensity);
            }
            else
            {
                return ApplyEffectWithCpu(originalBitmap, effectType, intensity);
            }
        }

        /// <summary>
        /// 使用GPU加速应用图像效果
        /// </summary>
        private SKBitmap ApplyEffectWithGpu(SKBitmap originalBitmap, string effectType, float intensity)
        {
            // 创建表面进行图像处理（SkiaSharp会自动选择最佳渲染后端）
            var imageInfo = new SKImageInfo(originalBitmap.Width, originalBitmap.Height, SKColorType.Rgba8888, SKAlphaType.Premul);
            using var surface = SKSurface.Create(imageInfo);

            if (surface == null)
            {
                throw new InvalidOperationException("无法创建图像表面");
            }

            var canvas = surface.Canvas;
            canvas.Clear(SKColors.Transparent);

            // 根据效果类型应用不同的滤镜
            using var paint = new SKPaint();

            ApplyEffectToCanvas(canvas, originalBitmap, paint, effectType, intensity);

            // 从表面获取结果
            using var image = surface.Snapshot();
            return SKBitmap.FromImage(image);
        }

        /// <summary>
        /// 使用CPU渲染应用图像效果
        /// </summary>
        private SKBitmap ApplyEffectWithCpu(SKBitmap originalBitmap, string effectType, float intensity)
        {
            // 强制使用CPU渲染
            var imageInfo = new SKImageInfo(originalBitmap.Width, originalBitmap.Height, SKColorType.Rgba8888, SKAlphaType.Premul);

            // 创建CPU位图
            var resultBitmap = new SKBitmap(imageInfo);
            using var canvas = new SKCanvas(resultBitmap);
            canvas.Clear(SKColors.Transparent);

            // 根据效果类型应用不同的滤镜
            using var paint = new SKPaint();

            ApplyEffectToCanvas(canvas, originalBitmap, paint, effectType, intensity);

            return resultBitmap;
        }

        /// <summary>
        /// 在画布上应用效果的通用方法
        /// </summary>
        private void ApplyEffectToCanvas(SKCanvas canvas, SKBitmap originalBitmap, SKPaint paint, string effectType, float intensity)
        {
            switch (effectType)
            {
                case "模糊":
                    ApplyBlurEffect(canvas, originalBitmap, paint, intensity);
                    break;
                case "亮度调整":
                    ApplyBrightnessEffect(canvas, originalBitmap, paint, intensity);
                    break;
                case "对比度调整":
                    ApplyContrastEffect(canvas, originalBitmap, paint, intensity);
                    break;
                case "锐化":
                    ApplySharpenEffect(canvas, originalBitmap, paint, intensity);
                    break;
                case "浮雕效果":
                    ApplyEmbossEffect(canvas, originalBitmap, paint, intensity);
                    break;
                default:
                    canvas.DrawBitmap(originalBitmap, 0, 0);
                    break;
            }
        }

        private void ApplyBlurEffect(SKCanvas canvas, SKBitmap bitmap, SKPaint paint, float intensity)
        {
            var blurRadius = intensity * 20.0f; // 最大模糊半径 20
            using var blurFilter = SKImageFilter.CreateBlur(blurRadius, blurRadius);
            paint.ImageFilter = blurFilter;
            canvas.DrawBitmap(bitmap, 0, 0, paint);
        }

        private void ApplyBrightnessEffect(SKCanvas canvas, SKBitmap bitmap, SKPaint paint, float intensity)
        {
            // intensity 范围 0-1，转换为 -100 到 +100 的亮度调整
            var brightness = (intensity - 0.5f) * 200.0f;
            
            var colorMatrix = new float[]
            {
                1, 0, 0, 0, brightness,
                0, 1, 0, 0, brightness,
                0, 0, 1, 0, brightness,
                0, 0, 0, 1, 0
            };

            using var colorFilter = SKColorFilter.CreateColorMatrix(colorMatrix);
            paint.ColorFilter = colorFilter;
            canvas.DrawBitmap(bitmap, 0, 0, paint);
        }

        private void ApplyContrastEffect(SKCanvas canvas, SKBitmap bitmap, SKPaint paint, float intensity)
        {
            // intensity 范围 0-1，转换为 0.5 到 2.0 的对比度
            var contrast = 0.5f + intensity * 1.5f;
            var offset = (1.0f - contrast) * 128.0f;

            var colorMatrix = new float[]
            {
                contrast, 0, 0, 0, offset,
                0, contrast, 0, 0, offset,
                0, 0, contrast, 0, offset,
                0, 0, 0, 1, 0
            };

            using var colorFilter = SKColorFilter.CreateColorMatrix(colorMatrix);
            paint.ColorFilter = colorFilter;
            canvas.DrawBitmap(bitmap, 0, 0, paint);
        }

        private void ApplySharpenEffect(SKCanvas canvas, SKBitmap bitmap, SKPaint paint, float intensity)
        {
            // 锐化卷积核
            var sharpenKernel = new float[]
            {
                0, -intensity, 0,
                -intensity, 1 + 4 * intensity, -intensity,
                0, -intensity, 0
            };

            using var sharpenFilter = SKImageFilter.CreateMatrixConvolution(
                new SKSizeI(3, 3),
                sharpenKernel,
                1.0f,
                0.0f,
                new SKPointI(1, 1),
                SKShaderTileMode.Clamp,
                false);

            paint.ImageFilter = sharpenFilter;
            canvas.DrawBitmap(bitmap, 0, 0, paint);
        }

        private void ApplyEmbossEffect(SKCanvas canvas, SKBitmap bitmap, SKPaint paint, float intensity)
        {
            // 浮雕效果卷积核
            var embossKernel = new float[]
            {
                -2 * intensity, -intensity, 0,
                -intensity, 1, intensity,
                0, intensity, 2 * intensity
            };

            using var embossFilter = SKImageFilter.CreateMatrixConvolution(
                new SKSizeI(3, 3),
                embossKernel,
                1.0f,
                128.0f,
                new SKPointI(1, 1),
                SKShaderTileMode.Clamp,
                false);

            paint.ImageFilter = embossFilter;
            canvas.DrawBitmap(bitmap, 0, 0, paint);
        }

        public void Dispose()
        {
            if (!disposed)
            {
                disposed = true;
            }
            GC.SuppressFinalize(this);
        }
    }
}
