# GPU 加速图像处理示例 - .NET 9 WinForms

这是一个使用 .NET 9 WinForms 和 SkiaSharp 实现的 GPU 加速图像处理应用程序示例。

## 功能特性

- **GPU 加速处理**: 使用 SkiaSharp 的硬件加速进行图像处理
- **多种图像效果**:
  - 模糊效果
  - 亮度调整
  - 对比度调整
  - 锐化效果
  - 浮雕效果
- **图片拼接功能**:
  - 导入多张图片
  - 自定义行列数（1-10行/列）
  - 多种输出比例选择（4:3、16:9、1:1等）
  - 保持原图片大小不变
  - 智能布局和居中对齐
- **实时预览**: 在 PictureBox 控件中实时显示处理结果
- **强度调节**: 通过滑块调节效果强度
- **性能监控**: 实时显示处理耗时和性能详情
- **性能对比**: 内置 SkiaSharp vs GDI+ 性能测试
- **图像保存**: 支持保存为 PNG、JPEG、BMP 格式

## 技术栈

- **.NET 9**: 最新的 .NET 框架
- **WinForms**: Windows 桌面应用程序框架
- **SkiaSharp**: 跨平台 2D 图形库，支持 GPU 加速
- **OpenTK**: OpenGL 绑定库，提供 GPU 上下文

## 系统要求

- Windows 10/11
- .NET 9 Runtime
- 支持 OpenGL 3.0+ 的显卡

## 使用方法

### 单张图片处理
1. 运行应用程序
2. 点击"加载图片"按钮选择要处理的图片
3. **可直接保存原图**：加载后即可点击"保存图片"保存原图
4. 在下拉框中选择要应用的效果（可选）
5. 使用滑块调节效果强度（可选）
6. 点击"应用效果"按钮处理图片（可选）
7. 点击"保存图片"按钮保存图片（原图或处理后的图片）

### 图片拼接功能
1. 在"图片拼接"组框中点击"导入多张图片"按钮
2. 选择要拼接的多张图片（支持多选）
3. 设置拼接的行数和列数（1-10）
4. 选择输出比例：
   - 自动：根据图片自动计算
   - 1:1：正方形
   - 4:3：标准比例
   - 3:2：经典比例
   - 16:9：宽屏比例
   - 16:10：宽屏比例
   - 21:9：超宽屏比例
5. 点击"开始拼接"按钮生成拼接图片
6. 拼接结果会显示在主界面中，保持每张图片的原始大小
7. **可直接保存拼接图片**：拼接完成后即可保存，无需额外处理
8. 可以对拼接结果应用图像效果（可选）
9. 点击"保存图片"保存图片（拼接图片或处理后的图片）

### 智能保存功能
- **即时保存**: 加载图片或完成拼接后立即可保存，无需额外处理
- **智能选择**: 自动保存最新的图片版本（处理后的图片优先，否则保存原图/拼接图片）
- **格式支持**: 支持保存为 PNG、JPEG、BMP 格式
- **状态提示**: 清楚显示保存的是原图、拼接图片还是处理后的图片

### 性能监控功能
- **实时耗时显示**: 所有操作都会显示实际耗时
- **详细性能分析**: 拼接操作显示加载、处理、总计时间
- **智能性能对比**: 性能测试使用相同的比例设置，确保公平对比
- **保持原图质量**: 性能测试中保持原图片大小和比例不变

## 构建和运行

```bash
# 还原 NuGet 包
dotnet restore

# 构建项目
dotnet build

# 运行应用程序
dotnet run
```

## 项目结构

- `Program.cs` - 应用程序入口点
- `MainForm.cs` - 主窗体界面和事件处理
- `MainForm.Designer.cs` - 设计器生成的界面布局代码
- `MainForm.resx` - 窗体资源文件
- `GpuImageProcessor.cs` - GPU 加速图像处理核心类
- `ImageStitcher.cs` - 图片拼接处理类
- `CreateTestImage.cs` - 测试图像生成器
- `SkiaSample.csproj` - 项目配置文件

## 输出比例功能详解

### 比例选项说明
- **自动**：根据图片网格自动计算输出尺寸
- **1:1 (正方形)**：输出正方形图片，适合社交媒体头像
- **4:3 (标准)**：传统显示器比例，适合打印和展示
- **3:2 (经典)**：经典摄影比例，适合相册制作
- **16:9 (宽屏)**：现代显示器比例，适合屏幕展示
- **16:10 (宽屏)**：宽屏显示器比例
- **21:9 (超宽屏)**：超宽屏比例，适合横幅设计

### 拼接算法特点
- **保持原图大小**：每张图片保持原始尺寸，不会被拉伸变形
- **智能布局**：图片在指定比例的画布中智能排列
- **居中对齐**：整个图片网格在输出画布中居中显示
- **背景填充**：空白区域用白色背景填充

## 性能监控详解

### 实时性能显示
应用程序会实时显示各种操作的耗时：

- **图片加载**: 显示单张或多张图片的加载时间
- **效果处理**: 显示图像效果应用的处理时间
- **图片拼接**: 显示详细的拼接性能分析
  - 加载时间：读取和解码图片文件的时间
  - 处理时间：图像拼接和渲染的时间
  - 总计时间：整个操作的总耗时

### 性能对比测试
点击"性能测试"按钮可以：
- **使用相同设置**: 自动使用当前选择的行列数和输出比例
- **保持原图质量**: 两种方法都保持原图片大小和比例不变
- **公平对比**: 确保 SkiaSharp 和 GDI+ 使用相同的处理参数
- **详细报告**: 显示每种方法的详细性能数据
  - 处理时间对比
  - 输出尺寸信息
  - 实际比例数据
  - 性能提升倍数

### 性能测试特点
- **智能比例处理**: 支持所有输出比例（自动、1:1、4:3、16:9等）
- **原图保真**: 确保图片不被拉伸或变形
- **居中布局**: 图片网格在指定比例画布中居中显示
- **一致性验证**: 验证两种方法输出的一致性

### 典型性能表现
- **SkiaSharp 通常比 GDI+ 快 2-5倍**
- **大图片和批量操作优势更明显**
- **复杂比例计算时 SkiaSharp 优势更突出**
- **硬件加速进一步提升性能**

## 注意事项

- 首次运行时可能需要初始化图像处理器，请稍等片刻
- 建议使用较小的图片进行测试（如 1920x1080 以下）
- 处理大图片时可能需要更多时间和内存
- 拼接时图片数量不能超过行数×列数

## 设计器支持

项目现在使用标准的 WinForms 设计器模式：
- **MainForm.Designer.cs**: 包含所有控件的设计时布局和属性
- **MainForm.resx**: 存储窗体资源和本地化信息
- **可视化设计**: 支持在 Visual Studio 设计器中可视化编辑界面

## 扩展功能

可以轻松扩展更多功能：

### 图像处理效果
- 色彩调整（饱和度、色相）
- 几何变换（旋转、缩放）
- 高级滤镜（油画效果、水彩效果）
- 批量处理功能

### 图片拼接增强
- ✅ 多种输出比例支持（4:3、16:9、1:1等）
- ✅ 保持原图片大小不变
- 自定义边框和间距
- 支持不同的拼接模式（瀑布流、马赛克等）
- 拖拽排序功能
- 模板保存和加载
- 批量处理多个拼接任务
