using System;
using System.IO;
using SkiaSharp;

namespace SkiaSample
{
    public static class TestImageCreator
    {
        public static void CreateSampleImages()
        {
            var imagesDir = "Images";
            if (!Directory.Exists(imagesDir))
            {
                Directory.CreateDirectory(imagesDir);
            }

            // 创建彩色渐变测试图像
            CreateGradientImage(Path.Combine(imagesDir, "gradient_test.png"));
            
            // 创建几何图形测试图像
            CreateGeometryImage(Path.Combine(imagesDir, "geometry_test.png"));
            
            // 创建文字测试图像
            CreateTextImage(Path.Combine(imagesDir, "text_test.png"));
        }

        private static void CreateGradientImage(string filePath)
        {
            var width = 800;
            var height = 600;
            
            using var bitmap = new SKBitmap(width, height);
            using var canvas = new SKCanvas(bitmap);
            
            // 创建彩虹渐变
            var colors = new SKColor[]
            {
                SKColors.Red,
                SKColors.Orange,
                SKColors.Yellow,
                SKColors.Green,
                SKColors.Blue,
                SKColors.Indigo,
                SKColors.Violet
            };
            
            using var shader = SKShader.CreateLinearGradient(
                new SKPoint(0, 0),
                new SKPoint(width, height),
                colors,
                null,
                SKShaderTileMode.Clamp);
            
            using var paint = new SKPaint
            {
                Shader = shader
            };
            
            canvas.DrawRect(0, 0, width, height, paint);
            
            // 保存图像
            using var image = SKImage.FromBitmap(bitmap);
            using var data = image.Encode(SKEncodedImageFormat.Png, 100);
            using var stream = File.OpenWrite(filePath);
            data.SaveTo(stream);
        }

        private static void CreateGeometryImage(string filePath)
        {
            var width = 800;
            var height = 600;
            
            using var bitmap = new SKBitmap(width, height);
            using var canvas = new SKCanvas(bitmap);
            
            canvas.Clear(SKColors.White);
            
            // 绘制各种几何图形
            using var paint = new SKPaint
            {
                IsAntialias = true,
                StrokeWidth = 3
            };
            
            // 红色圆形
            paint.Color = SKColors.Red;
            paint.Style = SKPaintStyle.Fill;
            canvas.DrawCircle(200, 150, 80, paint);
            
            // 蓝色矩形
            paint.Color = SKColors.Blue;
            canvas.DrawRect(350, 70, 160, 160, paint);
            
            // 绿色三角形
            paint.Color = SKColors.Green;
            using var path = new SKPath();
            path.MoveTo(600, 70);
            path.LineTo(550, 190);
            path.LineTo(650, 190);
            path.Close();
            canvas.DrawPath(path, paint);
            
            // 紫色椭圆
            paint.Color = SKColors.Purple;
            canvas.DrawOval(150, 300, 200, 120, paint);
            
            // 橙色星形
            paint.Color = SKColors.Orange;
            DrawStar(canvas, paint, 500, 380, 60, 5);
            
            // 保存图像
            using var image = SKImage.FromBitmap(bitmap);
            using var data = image.Encode(SKEncodedImageFormat.Png, 100);
            using var stream = File.OpenWrite(filePath);
            data.SaveTo(stream);
        }

        private static void CreateTextImage(string filePath)
        {
            var width = 800;
            var height = 600;
            
            using var bitmap = new SKBitmap(width, height);
            using var canvas = new SKCanvas(bitmap);
            
            canvas.Clear(SKColors.LightBlue);
            
            // 绘制标题文字
            using var titlePaint = new SKPaint
            {
                Color = SKColors.DarkBlue,
                TextSize = 48,
                IsAntialias = true,
                Typeface = SKTypeface.FromFamilyName("Arial", SKFontStyle.Bold),
                TextAlign = SKTextAlign.Center
            };
            
            canvas.DrawText("GPU 图像处理测试", width / 2, 100, titlePaint);
            
            // 绘制说明文字
            using var textPaint = new SKPaint
            {
                Color = SKColors.Black,
                TextSize = 24,
                IsAntialias = true,
                Typeface = SKTypeface.FromFamilyName("Arial"),
                TextAlign = SKTextAlign.Center
            };
            
            var lines = new string[]
            {
                "这是一个测试图像",
                "用于验证 GPU 加速图像处理功能",
                "支持多种效果：模糊、亮度、对比度等",
                "使用 SkiaSharp + OpenGL 实现"
            };
            
            var y = 200;
            foreach (var line in lines)
            {
                canvas.DrawText(line, width / 2, y, textPaint);
                y += 40;
            }
            
            // 绘制装饰性图案
            using var decorPaint = new SKPaint
            {
                Color = SKColors.Gold,
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 2,
                IsAntialias = true
            };
            
            canvas.DrawRect(50, 50, width - 100, height - 100, decorPaint);
            canvas.DrawRect(70, 70, width - 140, height - 140, decorPaint);
            
            // 保存图像
            using var image = SKImage.FromBitmap(bitmap);
            using var data = image.Encode(SKEncodedImageFormat.Png, 100);
            using var stream = File.OpenWrite(filePath);
            data.SaveTo(stream);
        }

        private static void DrawStar(SKCanvas canvas, SKPaint paint, float centerX, float centerY, float radius, int points)
        {
            using var path = new SKPath();
            var angle = -Math.PI / 2; // 从顶部开始
            var angleStep = Math.PI / points;
            
            for (int i = 0; i < points * 2; i++)
            {
                var r = (i % 2 == 0) ? radius : radius * 0.5f;
                var x = centerX + (float)(Math.Cos(angle) * r);
                var y = centerY + (float)(Math.Sin(angle) * r);
                
                if (i == 0)
                    path.MoveTo(x, y);
                else
                    path.LineTo(x, y);
                
                angle += angleStep;
            }
            
            path.Close();
            canvas.DrawPath(path, paint);
        }
    }
}
