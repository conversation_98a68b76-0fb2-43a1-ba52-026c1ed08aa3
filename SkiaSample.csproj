<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="SkiaSharp" Version="3.119.0" />
    <PackageReference Include="SkiaSharp.Views.WindowsForms" Version="3.119.0" />
    <PackageReference Include="SkiaSharp.Skottie" Version="3.119.0" />
    <PackageReference Include="OpenTK.Windowing.Desktop" Version="4.9.4" />
    <PackageReference Include="OpenTK.Graphics" Version="4.9.4" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.6" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Images\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
